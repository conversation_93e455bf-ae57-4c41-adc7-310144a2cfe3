"""
OAuth2 Authentication and Security Configuration

Centralized configuration for all OAuth2 and security-related services.
This file contains all timeouts, thresholds, and security parameters
used across the authentication system.
"""

from django.conf import settings
from decouple import config


class OAuth2SecurityConfig:
    """
    Centralized configuration for OAuth2 and security services
    """
    
    # ============================================================================
    # DEVICE VALIDATION SERVICE CONFIGURATION
    # ============================================================================
    
    # Device flow tracking timeouts
    DEVICE_FLOW_TIMEOUT = config('DEVICE_FLOW_TIMEOUT', default=3600, cast=int)  # 1 hour
    MAX_DEVICE_CHANGES = config('MAX_DEVICE_CHANGES', default=3, cast=int)
    
    # Device validation security thresholds
    DEVICE_VALIDATION_MIN_SCORE = config('DEVICE_VALIDATION_MIN_SCORE', default=60, cast=int)  # 60%
    DEVICE_VALIDATION_BLOCK_SCORE = config('DEVICE_VALIDATION_BLOCK_SCORE', default=30, cast=int)  # 30%
    
    # Cache keys for device tracking
    DEVICE_CACHE_KEYS = {
        'registration': "reg_device_{user_id}",
        'activation': "act_device_{user_id}",
        'login': "login_device_{user_id}",
    }
    
    # ============================================================================
    # SECURE TOKEN SERVICE CONFIGURATION
    # ============================================================================
    
    # Token expiration times (in hours)
    TOKEN_EXPIRATION = {
        'activation': config('ACTIVATION_TOKEN_HOURS', default=24, cast=int),
        'password_reset': config('PASSWORD_RESET_TOKEN_HOURS', default=4, cast=int),
        'email_verification': config('EMAIL_VERIFICATION_TOKEN_HOURS', default=2, cast=int),
        'phone_verification': config('PHONE_VERIFICATION_TOKEN_HOURS', default=2, cast=int),
    }
    
    # Maximum attempts before token invalidation
    TOKEN_MAX_ATTEMPTS = {
        'activation': config('ACTIVATION_MAX_ATTEMPTS', default=5, cast=int),
        'password_reset': config('PASSWORD_RESET_MAX_ATTEMPTS', default=3, cast=int),
        'email_verification': config('EMAIL_VERIFICATION_MAX_ATTEMPTS', default=3, cast=int),
        'phone_verification': config('PHONE_VERIFICATION_MAX_ATTEMPTS', default=3, cast=int),
    }
    
    # ============================================================================
    # ACCOUNT LOCKOUT SERVICE CONFIGURATION
    # ============================================================================
    
    # Failed login attempt settings
    FAILED_ATTEMPT_WINDOW_HOURS = config('FAILED_ATTEMPT_WINDOW_HOURS', default=5, cast=int)
    MAX_FAILED_ATTEMPTS = config('MAX_FAILED_ATTEMPTS', default=5, cast=int)
    
    # Lockout duration settings (in hours)
    LOCKOUT_DURATIONS = {
        'first': config('FIRST_LOCKOUT_HOURS', default=24, cast=int),      # 24 hours
        'second': config('SECOND_LOCKOUT_HOURS', default=48, cast=int),    # 48 hours  
        'third': config('THIRD_LOCKOUT_HOURS', default=168, cast=int),     # 1 week
        'permanent': config('PERMANENT_LOCKOUT', default=True, cast=bool), # Permanent after 3rd
    }
    
    # ============================================================================
    # RATE LIMITING SERVICE CONFIGURATION
    # ============================================================================
    
    # Rate limiting windows (in seconds)
    RATE_LIMIT_WINDOWS = {
        'login': config('LOGIN_RATE_LIMIT_WINDOW', default=3600, cast=int),        # 1 hour
        'registration': config('REGISTRATION_RATE_LIMIT_WINDOW', default=86400, cast=int),  # 24 hours
        'password_reset': config('PASSWORD_RESET_RATE_LIMIT_WINDOW', default=3600, cast=int),  # 1 hour
        'token_refresh': config('TOKEN_REFRESH_RATE_LIMIT_WINDOW', default=300, cast=int),     # 5 minutes
    }
    
    # Rate limiting thresholds
    RATE_LIMIT_THRESHOLDS = {
        'login': config('LOGIN_RATE_LIMIT_COUNT', default=10, cast=int),
        'registration': config('REGISTRATION_RATE_LIMIT_COUNT', default=5, cast=int),
        'password_reset': config('PASSWORD_RESET_RATE_LIMIT_COUNT', default=3, cast=int),
        'token_refresh': config('TOKEN_REFRESH_RATE_LIMIT_COUNT', default=20, cast=int),
    }
    
    # IP blocking configurations
    IP_BLOCK_THRESHOLDS = {
        'failed_logins': {
            'count': config('IP_BLOCK_FAILED_LOGINS_COUNT', default=10, cast=int),
            'window': config('IP_BLOCK_FAILED_LOGINS_WINDOW', default=3600, cast=int),
            'block_duration': config('IP_BLOCK_FAILED_LOGINS_DURATION', default=86400, cast=int),
        },
        'suspicious_requests': {
            'count': config('IP_BLOCK_SUSPICIOUS_COUNT', default=50, cast=int),
            'window': config('IP_BLOCK_SUSPICIOUS_WINDOW', default=3600, cast=int),
            'block_duration': config('IP_BLOCK_SUSPICIOUS_DURATION', default=3600, cast=int),
        },
        'brute_force': {
            'count': config('IP_BLOCK_BRUTE_FORCE_COUNT', default=20, cast=int),
            'window': config('IP_BLOCK_BRUTE_FORCE_WINDOW', default=900, cast=int),
            'block_duration': config('IP_BLOCK_BRUTE_FORCE_DURATION', default=604800, cast=int),  # 7 days
        },
    }
    
    # Cache prefixes for rate limiting
    RATE_LIMIT_CACHE_PREFIXES = {
        'rate_limit': "rate_limit_",
        'ip_block': "ip_block_",
        'suspicious_ip': "suspicious_ip_",
    }
    
    # ============================================================================
    # TOKEN INVALIDATION SERVICE CONFIGURATION
    # ============================================================================
    
    # Token blacklist cache settings
    BLACKLIST_CACHE_PREFIX = "token_blacklist_"
    BLACKLIST_CACHE_TIMEOUT = config('BLACKLIST_CACHE_TIMEOUT', default=604800, cast=int)  # 7 days
    
    # Invalidation reasons
    INVALIDATION_REASONS = {
        'security_breach': "security_breach",
        'compromised_device': "compromised_device", 
        'suspicious_activity': "suspicious_activity",
        'admin_action': "admin_action",
        'user_request': "user_request",
        'account_lockout': "account_lockout",
        'password_change': "password_change",
    }
    
    # ============================================================================
    # JWT TOKEN ROTATION CONFIGURATION
    # ============================================================================
    
    # JWT token settings (from OAuth2 provider settings)
    ACCESS_TOKEN_LIFETIME = config('ACCESS_TOKEN_LIFETIME_SECONDS', default=900, cast=int)    # 15 minutes
    REFRESH_TOKEN_LIFETIME = config('REFRESH_TOKEN_LIFETIME_SECONDS', default=604800, cast=int)  # 7 days
    
    # Token rotation settings
    ROTATE_REFRESH_TOKEN = config('ROTATE_REFRESH_TOKEN', default=True, cast=bool)
    TOKEN_ROTATION_GRACE_PERIOD = config('TOKEN_ROTATION_GRACE_PERIOD', default=300, cast=int)  # 5 minutes
    
    # ============================================================================
    # AUDIT TRAIL CONFIGURATION
    # ============================================================================
    
    # Audit log retention (in days)
    AUDIT_LOG_RETENTION_DAYS = config('AUDIT_LOG_RETENTION_DAYS', default=365, cast=int)  # 1 year
    
    # Security event types for enhanced logging
    SECURITY_EVENT_TYPES = {
        'login_success': 'login_success',
        'login_failed': 'login_failed',
        'registration': 'registration',
        'activation': 'activation',
        'password_reset': 'password_reset',
        'token_refresh': 'token_refresh',
        'device_validation': 'device_validation',
        'suspicious_activity': 'suspicious_activity',
        'account_lockout': 'account_lockout',
        'security_breach': 'security_breach',
    }
    
    # ============================================================================
    # OTP AND NEW DEVICE VERIFICATION
    # ============================================================================
    
    # OTP settings
    OTP_LENGTH = config('OTP_LENGTH', default=6, cast=int)
    OTP_EXPIRY_SECONDS = config('OTP_EXPIRY_SECONDS', default=300, cast=int)  # 5 minutes
    OTP_MAX_ATTEMPTS = config('OTP_MAX_ATTEMPTS', default=3, cast=int)
    
    # New device verification
    NEW_DEVICE_VERIFICATION_REQUIRED = config('NEW_DEVICE_VERIFICATION_REQUIRED', default=True, cast=bool)
    DEVICE_FINGERPRINT_ALGORITHM = config('DEVICE_FINGERPRINT_ALGORITHM', default='sha256')
    
    # ============================================================================
    # SECURITY MONITORING THRESHOLDS
    # ============================================================================
    
    # Health score thresholds for monitoring
    HEALTH_SCORE_THRESHOLDS = {
        'excellent': config('HEALTH_SCORE_EXCELLENT', default=90, cast=int),
        'good': config('HEALTH_SCORE_GOOD', default=75, cast=int),
        'fair': config('HEALTH_SCORE_FAIR', default=60, cast=int),
        'poor': config('HEALTH_SCORE_POOR', default=0, cast=int),
    }
    
    # Token cleanup thresholds
    TOKEN_CLEANUP_THRESHOLDS = {
        'expired_token_percentage': config('EXPIRED_TOKEN_CLEANUP_THRESHOLD', default=20, cast=int),
        'cleanup_batch_size': config('TOKEN_CLEANUP_BATCH_SIZE', default=1000, cast=int),
        'cleanup_interval_hours': config('TOKEN_CLEANUP_INTERVAL_HOURS', default=24, cast=int),
    }
    
    # ============================================================================
    # ENVIRONMENT-SPECIFIC OVERRIDES
    # ============================================================================
    
    @classmethod
    def is_development(cls):
        """Check if running in development mode"""
        return getattr(settings, 'DEBUG', False)
    
    @classmethod
    def is_production(cls):
        """Check if running in production mode"""
        return not cls.is_development()
    
    @classmethod
    def get_config_summary(cls):
        """Get a summary of current configuration for debugging"""
        return {
            'device_flow_timeout': cls.DEVICE_FLOW_TIMEOUT,
            'device_validation_min_score': cls.DEVICE_VALIDATION_MIN_SCORE,
            'token_expiration': cls.TOKEN_EXPIRATION,
            'lockout_durations': cls.LOCKOUT_DURATIONS,
            'rate_limit_thresholds': cls.RATE_LIMIT_THRESHOLDS,
            'access_token_lifetime': cls.ACCESS_TOKEN_LIFETIME,
            'refresh_token_lifetime': cls.REFRESH_TOKEN_LIFETIME,
            'environment': 'development' if cls.is_development() else 'production',
        }


# Global configuration instance
oauth2_security_config = OAuth2SecurityConfig()
