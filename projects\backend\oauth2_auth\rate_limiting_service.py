"""
Rate Limiting and DDoS Protection Service

Provides sophisticated rate limiting, IP-based blocking, and geographic access controls
for fintech-grade security protection.
"""

import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import timedelta
from django.utils import timezone
from django.core.cache import cache
from django.contrib.auth import get_user_model
from .utils import log_security_event, get_client_ip
from .config import oauth2_security_config

User = get_user_model()
logger = logging.getLogger(__name__)


class RateLimitingService:
    """
    Service for rate limiting and DDoS protection
    """
    
    # Use centralized configuration
    RATE_LIMITS = {
        'login': {
            'requests': oauth2_security_config.RATE_LIMIT_THRESHOLDS['login'],
            'window': oauth2_security_config.RATE_LIMIT_WINDOWS['login']
        },
        'registration': {
            'requests': oauth2_security_config.RATE_LIMIT_THRESHOLDS['registration'],
            'window': oauth2_security_config.RATE_LIMIT_WINDOWS['registration']
        },
        'password_reset': {
            'requests': oauth2_security_config.RATE_LIMIT_THRESHOLDS['password_reset'],
            'window': oauth2_security_config.RATE_LIMIT_WINDOWS['password_reset']
        },
        'activation': {'requests': 10, 'window': 3600},   # Keep as is for now
        'api_general': {'requests': 100, 'window': 3600}, # Keep as is for now
        'token_refresh': {
            'requests': oauth2_security_config.RATE_LIMIT_THRESHOLDS['token_refresh'],
            'window': oauth2_security_config.RATE_LIMIT_WINDOWS['token_refresh']
        },
    }

    # IP blocking configurations - use centralized config
    IP_BLOCK_THRESHOLDS = oauth2_security_config.IP_BLOCK_THRESHOLDS

    # Cache prefixes - use centralized config
    RATE_LIMIT_PREFIX = oauth2_security_config.RATE_LIMIT_CACHE_PREFIXES['rate_limit']
    IP_BLOCK_PREFIX = oauth2_security_config.RATE_LIMIT_CACHE_PREFIXES['ip_block']
    SUSPICIOUS_IP_PREFIX = oauth2_security_config.RATE_LIMIT_CACHE_PREFIXES['suspicious_ip']
    
    @classmethod
    def check_rate_limit(cls, identifier: str, action: str, request=None) -> Tuple[bool, Dict[str, Any]]:
        """
        Check if an action is rate limited for a given identifier
        
        Args:
            identifier: Unique identifier (IP, user ID, etc.)
            action: Action type ('login', 'registration', etc.)
            request: HTTP request object (optional)
            
        Returns:
            Tuple of (is_allowed, rate_limit_info)
        """
        try:
            if action not in cls.RATE_LIMITS:
                return True, {'message': 'No rate limit configured'}
            
            config = cls.RATE_LIMITS[action]
            cache_key = f"{cls.RATE_LIMIT_PREFIX}{action}_{identifier}"
            
            # Get current request count
            current_requests = cache.get(cache_key, 0)
            
            if current_requests >= config['requests']:
                # Rate limit exceeded
                ttl = cache.ttl(cache_key)
                remaining_time = ttl if ttl > 0 else config['window']
                
                # Log rate limit violation
                cls._log_rate_limit_violation(identifier, action, current_requests, request)
                
                return False, {
                    'rate_limited': True,
                    'action': action,
                    'limit': config['requests'],
                    'window': config['window'],
                    'current_requests': current_requests,
                    'remaining_time': remaining_time,
                    'message': f'Rate limit exceeded for {action}. Try again in {remaining_time} seconds.'
                }
            
            # Increment request count
            cache.set(cache_key, current_requests + 1, config['window'])
            
            return True, {
                'rate_limited': False,
                'action': action,
                'limit': config['requests'],
                'current_requests': current_requests + 1,
                'remaining_requests': config['requests'] - (current_requests + 1)
            }
            
        except Exception as e:
            logger.error(f"Error checking rate limit: {str(e)}")
            return True, {'error': str(e)}  # Allow on error to prevent blocking legitimate users
    
    @classmethod
    def check_ip_block(cls, ip_address: str, request=None) -> Tuple[bool, Dict[str, Any]]:
        """
        Check if an IP address is blocked
        
        Args:
            ip_address: IP address to check
            request: HTTP request object (optional)
            
        Returns:
            Tuple of (is_blocked, block_info)
        """
        try:
            cache_key = f"{cls.IP_BLOCK_PREFIX}{ip_address}"
            block_info = cache.get(cache_key)
            
            if block_info:
                remaining_time = cache.ttl(cache_key)
                
                # Log blocked access attempt
                log_security_event(
                    event_type='blocked_ip_access_attempt',
                    description=f'Access attempt from blocked IP: {ip_address}',
                    ip_address=ip_address,
                    user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
                    metadata={
                        'block_reason': block_info.get('reason'),
                        'block_count': block_info.get('count'),
                        'remaining_time': remaining_time
                    }
                )
                
                return True, {
                    'blocked': True,
                    'reason': block_info.get('reason'),
                    'blocked_at': block_info.get('blocked_at'),
                    'remaining_time': remaining_time,
                    'message': f'IP address blocked due to {block_info.get("reason")}. Unblocked in {remaining_time} seconds.'
                }
            
            return False, {'blocked': False}
            
        except Exception as e:
            logger.error(f"Error checking IP block: {str(e)}")
            return False, {'error': str(e)}
    
    @classmethod
    def block_ip(cls, ip_address: str, reason: str, duration: int = None, request=None) -> bool:
        """
        Block an IP address
        
        Args:
            ip_address: IP address to block
            reason: Reason for blocking
            duration: Block duration in seconds (optional)
            request: HTTP request object (optional)
            
        Returns:
            bool: Success status
        """
        try:
            if not duration:
                # Use default duration based on reason
                duration = cls.IP_BLOCK_THRESHOLDS.get(reason, {}).get('block_duration', 3600)
            
            cache_key = f"{cls.IP_BLOCK_PREFIX}{ip_address}"
            block_info = {
                'reason': reason,
                'blocked_at': timezone.now().isoformat(),
                'duration': duration,
                'count': 1
            }
            
            # Check if IP is already blocked and increment count
            existing_block = cache.get(cache_key)
            if existing_block:
                block_info['count'] = existing_block.get('count', 0) + 1
            
            cache.set(cache_key, block_info, duration)
            
            # Log IP blocking
            log_security_event(
                event_type='ip_address_blocked',
                description=f'IP address blocked: {reason}',
                ip_address=ip_address,
                user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
                metadata={
                    'reason': reason,
                    'duration': duration,
                    'block_count': block_info['count']
                }
            )
            
            logger.warning(f"Blocked IP {ip_address} for {reason} (duration: {duration}s)")
            return True
            
        except Exception as e:
            logger.error(f"Error blocking IP: {str(e)}")
            return False
    
    @classmethod
    def track_suspicious_activity(cls, ip_address: str, activity_type: str, request=None) -> bool:
        """
        Track suspicious activity and auto-block if thresholds are exceeded
        
        Args:
            ip_address: IP address to track
            activity_type: Type of suspicious activity
            request: HTTP request object (optional)
            
        Returns:
            bool: True if IP was blocked due to suspicious activity
        """
        try:
            cache_key = f"{cls.SUSPICIOUS_IP_PREFIX}{activity_type}_{ip_address}"
            current_count = cache.get(cache_key, 0)
            new_count = current_count + 1
            
            # Set cache with sliding window
            cache.set(cache_key, new_count, 3600)  # 1 hour window
            
            # Check if threshold is exceeded
            threshold_config = cls.IP_BLOCK_THRESHOLDS.get(activity_type)
            if threshold_config and new_count >= threshold_config['count']:
                # Block the IP
                cls.block_ip(
                    ip_address=ip_address,
                    reason=activity_type,
                    duration=threshold_config['block_duration'],
                    request=request
                )
                
                # Clear the tracking counter
                cache.delete(cache_key)
                
                return True
            
            # Log suspicious activity
            log_security_event(
                event_type='suspicious_activity_tracked',
                description=f'Suspicious activity tracked: {activity_type}',
                ip_address=ip_address,
                user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
                metadata={
                    'activity_type': activity_type,
                    'count': new_count,
                    'threshold': threshold_config['count'] if threshold_config else 'N/A'
                }
            )
            
            return False
            
        except Exception as e:
            logger.error(f"Error tracking suspicious activity: {str(e)}")
            return False
    
    @classmethod
    def unblock_ip(cls, ip_address: str, admin_user=None, request=None) -> bool:
        """
        Manually unblock an IP address
        
        Args:
            ip_address: IP address to unblock
            admin_user: Admin user performing the action (optional)
            request: HTTP request object (optional)
            
        Returns:
            bool: Success status
        """
        try:
            cache_key = f"{cls.IP_BLOCK_PREFIX}{ip_address}"
            block_info = cache.get(cache_key)
            
            if not block_info:
                return False  # IP was not blocked
            
            cache.delete(cache_key)
            
            # Log IP unblocking
            log_security_event(
                event_type='ip_address_unblocked',
                description=f'IP address manually unblocked',
                ip_address=ip_address,
                user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
                metadata={
                    'admin_user': admin_user.email if admin_user else 'System',
                    'previous_block_reason': block_info.get('reason'),
                    'block_count': block_info.get('count')
                }
            )
            
            logger.info(f"Unblocked IP {ip_address} by {admin_user.email if admin_user else 'System'}")
            return True
            
        except Exception as e:
            logger.error(f"Error unblocking IP: {str(e)}")
            return False
    
    @classmethod
    def get_rate_limit_status(cls, identifier: str, action: str) -> Dict[str, Any]:
        """
        Get current rate limit status for an identifier and action
        
        Args:
            identifier: Unique identifier
            action: Action type
            
        Returns:
            dict: Rate limit status
        """
        try:
            if action not in cls.RATE_LIMITS:
                return {'error': 'Invalid action'}
            
            config = cls.RATE_LIMITS[action]
            cache_key = f"{cls.RATE_LIMIT_PREFIX}{action}_{identifier}"
            current_requests = cache.get(cache_key, 0)
            ttl = cache.ttl(cache_key)
            
            return {
                'action': action,
                'limit': config['requests'],
                'window': config['window'],
                'current_requests': current_requests,
                'remaining_requests': max(0, config['requests'] - current_requests),
                'reset_time': ttl if ttl > 0 else 0,
                'rate_limited': current_requests >= config['requests']
            }
            
        except Exception as e:
            logger.error(f"Error getting rate limit status: {str(e)}")
            return {'error': str(e)}
    
    @classmethod
    def _log_rate_limit_violation(cls, identifier: str, action: str, request_count: int, request=None):
        """Log rate limit violation"""
        try:
            log_security_event(
                event_type='rate_limit_exceeded',
                description=f'Rate limit exceeded for {action}',
                ip_address=get_client_ip(request) if request else None,
                user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
                metadata={
                    'identifier': identifier,
                    'action': action,
                    'request_count': request_count,
                    'limit': cls.RATE_LIMITS[action]['requests']
                }
            )
        except Exception as e:
            logger.error(f"Error logging rate limit violation: {str(e)}")


# Global service instance
rate_limiting_service = RateLimitingService()


class RateLimitMiddleware:
    """
    Middleware for applying rate limiting to requests
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Check IP blocking first
        client_ip = get_client_ip(request)
        is_blocked, block_info = rate_limiting_service.check_ip_block(client_ip, request)

        if is_blocked:
            from django.http import JsonResponse
            return JsonResponse({
                'code': 'IP_BLOCKED',
                'message': block_info.get('message', 'IP address is blocked'),
                'details': 'Your IP address has been temporarily blocked due to suspicious activity',
                'actions': ['contact_support', 'try_different_network']
            }, status=429)

        # Apply rate limiting based on path
        action = self._get_action_from_path(request.path)
        if action:
            identifier = f"{client_ip}_{request.user.id if hasattr(request, 'user') and request.user.is_authenticated else 'anonymous'}"
            is_allowed, rate_info = rate_limiting_service.check_rate_limit(identifier, action, request)

            if not is_allowed:
                from django.http import JsonResponse
                return JsonResponse({
                    'code': 'RATE_LIMITED',
                    'message': rate_info.get('message', 'Rate limit exceeded'),
                    'details': f'Too many {action} attempts. Please try again later.',
                    'actions': ['wait_and_retry'],
                    'retry_after': rate_info.get('remaining_time', 300)
                }, status=429)

        response = self.get_response(request)
        return response

    def _get_action_from_path(self, path: str) -> Optional[str]:
        """Map request path to rate limiting action"""
        if '/api/auth/login/' in path:
            return 'login'
        elif '/api/auth/register/' in path:
            return 'registration'
        elif '/api/auth/password-reset/' in path:
            return 'password_reset'
        elif '/api/auth/activate-account/' in path:
            return 'activation'
        elif '/o/token/' in path:
            return 'token_refresh'
        elif path.startswith('/api/'):
            return 'api_general'
        return None
