# ============================================================================
# OAuth2 Authentication and Security Configuration
# ============================================================================
# Copy this file to .env and configure the values for your environment
# All timeouts are in seconds unless specified otherwise

# ============================================================================
# DEVICE VALIDATION SERVICE
# ============================================================================

# Device flow tracking timeout (default: 3600 = 1 hour)
DEVICE_FLOW_TIMEOUT=3600

# Maximum device changes allowed in authentication flow (default: 3)
MAX_DEVICE_CHANGES=3

# Minimum security score required for device validation (default: 60%)
DEVICE_VALIDATION_MIN_SCORE=60

# Security score threshold below which activation is blocked (default: 30%)
DEVICE_VALIDATION_BLOCK_SCORE=30

# ============================================================================
# SECURE TOKEN SERVICE
# ============================================================================

# Token expiration times (in hours)
ACTIVATION_TOKEN_HOURS=24
PASSWORD_RESET_TOKEN_HOURS=4
EMAIL_VERIFICATION_TOKEN_HOURS=2
PHONE_VERIFICATION_TOKEN_HOURS=2

# Maximum attempts before token invalidation
ACTIVATION_MAX_ATTEMPTS=5
PASSWORD_RESET_MAX_ATTEMPTS=3
EMAIL_VERIFICATION_MAX_ATTEMPTS=3
PHONE_VERIFICATION_MAX_ATTEMPTS=3

# ============================================================================
# ACCOUNT LOCKOUT SERVICE
# ============================================================================

# Time window for counting failed attempts (in hours, default: 5)
FAILED_ATTEMPT_WINDOW_HOURS=5

# Maximum failed attempts before lockout (default: 5)
MAX_FAILED_ATTEMPTS=5

# Lockout durations (in hours)
FIRST_LOCKOUT_HOURS=24      # 24 hours
SECOND_LOCKOUT_HOURS=48     # 48 hours
THIRD_LOCKOUT_HOURS=168     # 1 week
PERMANENT_LOCKOUT=true      # Permanent lockout after 3rd offense

# ============================================================================
# RATE LIMITING SERVICE
# ============================================================================

# Rate limiting time windows (in seconds)
LOGIN_RATE_LIMIT_WINDOW=3600        # 1 hour
REGISTRATION_RATE_LIMIT_WINDOW=86400 # 24 hours
PASSWORD_RESET_RATE_LIMIT_WINDOW=3600 # 1 hour
TOKEN_REFRESH_RATE_LIMIT_WINDOW=300   # 5 minutes

# Rate limiting thresholds (requests per window)
LOGIN_RATE_LIMIT_COUNT=10
REGISTRATION_RATE_LIMIT_COUNT=5
PASSWORD_RESET_RATE_LIMIT_COUNT=3
TOKEN_REFRESH_RATE_LIMIT_COUNT=20

# IP blocking thresholds
IP_BLOCK_FAILED_LOGINS_COUNT=10
IP_BLOCK_FAILED_LOGINS_WINDOW=3600    # 1 hour
IP_BLOCK_FAILED_LOGINS_DURATION=86400 # 24 hours

IP_BLOCK_SUSPICIOUS_COUNT=50
IP_BLOCK_SUSPICIOUS_WINDOW=3600       # 1 hour
IP_BLOCK_SUSPICIOUS_DURATION=3600     # 1 hour

IP_BLOCK_BRUTE_FORCE_COUNT=20
IP_BLOCK_BRUTE_FORCE_WINDOW=900       # 15 minutes
IP_BLOCK_BRUTE_FORCE_DURATION=604800  # 7 days

# ============================================================================
# TOKEN INVALIDATION SERVICE
# ============================================================================

# Token blacklist cache timeout (in seconds, default: 604800 = 7 days)
BLACKLIST_CACHE_TIMEOUT=604800

# ============================================================================
# JWT TOKEN ROTATION
# ============================================================================

# JWT token lifetimes (in seconds)
ACCESS_TOKEN_LIFETIME_SECONDS=900    # 15 minutes
REFRESH_TOKEN_LIFETIME_SECONDS=604800 # 7 days

# Token rotation settings
ROTATE_REFRESH_TOKEN=true
TOKEN_ROTATION_GRACE_PERIOD=300      # 5 minutes

# ============================================================================
# AUDIT TRAIL
# ============================================================================

# Audit log retention (in days, default: 365 = 1 year)
AUDIT_LOG_RETENTION_DAYS=365

# ============================================================================
# OTP AND NEW DEVICE VERIFICATION
# ============================================================================

# OTP settings
OTP_LENGTH=6
OTP_EXPIRY_SECONDS=300               # 5 minutes
OTP_MAX_ATTEMPTS=3

# New device verification
NEW_DEVICE_VERIFICATION_REQUIRED=true
DEVICE_FINGERPRINT_ALGORITHM=sha256

# ============================================================================
# SECURITY MONITORING
# ============================================================================

# Health score thresholds (percentages)
HEALTH_SCORE_EXCELLENT=90
HEALTH_SCORE_GOOD=75
HEALTH_SCORE_FAIR=60
HEALTH_SCORE_POOR=0

# Token cleanup settings
EXPIRED_TOKEN_CLEANUP_THRESHOLD=20   # Cleanup when >20% tokens are expired
TOKEN_CLEANUP_BATCH_SIZE=1000
TOKEN_CLEANUP_INTERVAL_HOURS=24

# ============================================================================
# ENVIRONMENT-SPECIFIC CONFIGURATIONS
# ============================================================================

# ============================================================================
# DEVELOPMENT/TEST SERVER CONFIGURATION
# ============================================================================
# Use these values for development and testing environments
# Copy the values below and uncomment them for development

# Device validation - shorter timeouts for faster testing
# DEVICE_FLOW_TIMEOUT=300              # 5 minutes instead of 1 hour
# DEVICE_VALIDATION_MIN_SCORE=50       # Lower threshold for testing
# DEVICE_VALIDATION_BLOCK_SCORE=20     # Lower block threshold

# Token expiration - shorter for testing
# ACTIVATION_TOKEN_HOURS=1             # 1 hour instead of 24 hours
# PASSWORD_RESET_TOKEN_HOURS=1         # 1 hour instead of 4 hours
# EMAIL_VERIFICATION_TOKEN_HOURS=1     # 1 hour instead of 2 hours

# JWT tokens - shorter lifetimes for testing
# ACCESS_TOKEN_LIFETIME_SECONDS=300    # 5 minutes instead of 15 minutes
# REFRESH_TOKEN_LIFETIME_SECONDS=3600  # 1 hour instead of 7 days

# Account lockout - more lenient for testing
# MAX_FAILED_ATTEMPTS=10               # More attempts allowed
# FAILED_ATTEMPT_WINDOW_HOURS=1        # Shorter window
# FIRST_LOCKOUT_HOURS=1                # 1 hour instead of 24 hours
# SECOND_LOCKOUT_HOURS=2               # 2 hours instead of 48 hours
# THIRD_LOCKOUT_HOURS=4                # 4 hours instead of 1 week

# Rate limiting - more lenient for testing
# LOGIN_RATE_LIMIT_COUNT=20            # More login attempts
# REGISTRATION_RATE_LIMIT_COUNT=10     # More registration attempts
# PASSWORD_RESET_RATE_LIMIT_COUNT=10   # More password reset attempts

# Token cleanup - smaller batches for testing
# TOKEN_CLEANUP_BATCH_SIZE=100         # Smaller cleanup batches
# EXPIRED_TOKEN_CLEANUP_THRESHOLD=10   # Cleanup at 10% instead of 20%

# OTP settings - shorter expiry for testing
# OTP_EXPIRY_SECONDS=120               # 2 minutes instead of 5 minutes

# ============================================================================
# PRODUCTION SERVER CONFIGURATION
# ============================================================================
# Use these values for production environments
# Copy the values below and uncomment them for production

# Device validation - stricter security
# DEVICE_FLOW_TIMEOUT=1800             # 30 minutes instead of 1 hour
# DEVICE_VALIDATION_MIN_SCORE=70       # Higher security score requirement
# DEVICE_VALIDATION_BLOCK_SCORE=40     # Higher block threshold

# Token expiration - secure defaults
# ACTIVATION_TOKEN_HOURS=12            # 12 hours instead of 24 hours
# PASSWORD_RESET_TOKEN_HOURS=2         # 2 hours instead of 4 hours
# EMAIL_VERIFICATION_TOKEN_HOURS=1     # 1 hour instead of 2 hours

# JWT tokens - secure lifetimes
# ACCESS_TOKEN_LIFETIME_SECONDS=600    # 10 minutes instead of 15 minutes
# REFRESH_TOKEN_LIFETIME_SECONDS=432000 # 5 days instead of 7 days

# Account lockout - stricter security
# MAX_FAILED_ATTEMPTS=3                # Stricter lockout
# FAILED_ATTEMPT_WINDOW_HOURS=3        # Shorter window
# FIRST_LOCKOUT_HOURS=48               # 48 hours instead of 24 hours
# SECOND_LOCKOUT_HOURS=168             # 1 week instead of 48 hours
# THIRD_LOCKOUT_HOURS=720              # 1 month instead of 1 week

# Rate limiting - stricter limits
# LOGIN_RATE_LIMIT_COUNT=5             # Stricter rate limiting
# REGISTRATION_RATE_LIMIT_COUNT=3      # Stricter registration limits
# PASSWORD_RESET_RATE_LIMIT_COUNT=2    # Stricter password reset limits

# IP blocking - more aggressive
# IP_BLOCK_FAILED_LOGINS_COUNT=5       # Block after fewer failures
# IP_BLOCK_SUSPICIOUS_COUNT=25         # Lower suspicious activity threshold
# IP_BLOCK_BRUTE_FORCE_COUNT=10        # Lower brute force threshold

# Token cleanup - larger batches for efficiency
# TOKEN_CLEANUP_BATCH_SIZE=2000        # Larger cleanup batches
# EXPIRED_TOKEN_CLEANUP_THRESHOLD=15   # Cleanup at 15% instead of 20%

# OTP settings - standard security
# OTP_EXPIRY_SECONDS=180               # 3 minutes instead of 5 minutes
# OTP_MAX_ATTEMPTS=2                   # Fewer OTP attempts

# Audit and monitoring - longer retention
# AUDIT_LOG_RETENTION_DAYS=730         # 2 years instead of 1 year

# ============================================================================
# QUICK ENVIRONMENT SETUP
# ============================================================================

# To quickly switch between environments, uncomment one of these sections:

# FOR DEVELOPMENT:
# Copy and uncomment the development values above

# FOR TESTING:
# Use development values but with even shorter timeouts if needed

# FOR PRODUCTION:
# Copy and uncomment the production values above

# ============================================================================
# SECURITY RECOMMENDATIONS BY ENVIRONMENT
# ============================================================================

# DEVELOPMENT:
# - Use shorter token lifetimes for faster testing cycles
# - More lenient rate limiting to avoid blocking during development
# - Lower security thresholds for easier testing
# - Smaller cleanup batches to reduce resource usage

# PRODUCTION:
# - Use stricter security settings
# - Shorter token lifetimes for better security
# - More aggressive rate limiting and IP blocking
# - Higher security score requirements
# - Longer audit log retention for compliance
