# ============================================================================
# OAuth2 Authentication and Security Configuration
# ============================================================================
# Copy this file to .env and configure the values for your environment
# All timeouts are in seconds unless specified otherwise

# ============================================================================
# DEVICE VALIDATION SERVICE
# ============================================================================

# Device flow tracking timeout (default: 3600 = 1 hour)
DEVICE_FLOW_TIMEOUT=3600

# Maximum device changes allowed in authentication flow (default: 3)
MAX_DEVICE_CHANGES=3

# Minimum security score required for device validation (default: 60%)
DEVICE_VALIDATION_MIN_SCORE=60

# Security score threshold below which activation is blocked (default: 30%)
DEVICE_VALIDATION_BLOCK_SCORE=30

# ============================================================================
# SECURE TOKEN SERVICE
# ============================================================================

# Token expiration times (in hours)
ACTIVATION_TOKEN_HOURS=24
PASSWORD_RESET_TOKEN_HOURS=4
EMAIL_VERIFICATION_TOKEN_HOURS=2
PHONE_VERIFICATION_TOKEN_HOURS=2

# Maximum attempts before token invalidation
ACTIVATION_MAX_ATTEMPTS=5
PASSWORD_RESET_MAX_ATTEMPTS=3
EMAIL_VERIFICATION_MAX_ATTEMPTS=3
PHONE_VERIFICATION_MAX_ATTEMPTS=3

# ============================================================================
# ACCOUNT LOCKOUT SERVICE
# ============================================================================

# Time window for counting failed attempts (in hours, default: 5)
FAILED_ATTEMPT_WINDOW_HOURS=5

# Maximum failed attempts before lockout (default: 5)
MAX_FAILED_ATTEMPTS=5

# Lockout durations (in hours)
FIRST_LOCKOUT_HOURS=24      # 24 hours
SECOND_LOCKOUT_HOURS=48     # 48 hours
THIRD_LOCKOUT_HOURS=168     # 1 week
PERMANENT_LOCKOUT=true      # Permanent lockout after 3rd offense

# ============================================================================
# RATE LIMITING SERVICE
# ============================================================================

# Rate limiting time windows (in seconds)
LOGIN_RATE_LIMIT_WINDOW=3600        # 1 hour
REGISTRATION_RATE_LIMIT_WINDOW=86400 # 24 hours
PASSWORD_RESET_RATE_LIMIT_WINDOW=3600 # 1 hour
TOKEN_REFRESH_RATE_LIMIT_WINDOW=300   # 5 minutes

# Rate limiting thresholds (requests per window)
LOGIN_RATE_LIMIT_COUNT=10
REGISTRATION_RATE_LIMIT_COUNT=5
PASSWORD_RESET_RATE_LIMIT_COUNT=3
TOKEN_REFRESH_RATE_LIMIT_COUNT=20

# IP blocking thresholds
IP_BLOCK_FAILED_LOGINS_COUNT=10
IP_BLOCK_FAILED_LOGINS_WINDOW=3600    # 1 hour
IP_BLOCK_FAILED_LOGINS_DURATION=86400 # 24 hours

IP_BLOCK_SUSPICIOUS_COUNT=50
IP_BLOCK_SUSPICIOUS_WINDOW=3600       # 1 hour
IP_BLOCK_SUSPICIOUS_DURATION=3600     # 1 hour

IP_BLOCK_BRUTE_FORCE_COUNT=20
IP_BLOCK_BRUTE_FORCE_WINDOW=900       # 15 minutes
IP_BLOCK_BRUTE_FORCE_DURATION=604800  # 7 days

# ============================================================================
# TOKEN INVALIDATION SERVICE
# ============================================================================

# Token blacklist cache timeout (in seconds, default: 604800 = 7 days)
BLACKLIST_CACHE_TIMEOUT=604800

# ============================================================================
# JWT TOKEN ROTATION
# ============================================================================

# JWT token lifetimes (in seconds)
ACCESS_TOKEN_LIFETIME_SECONDS=900    # 15 minutes
REFRESH_TOKEN_LIFETIME_SECONDS=604800 # 7 days

# Token rotation settings
ROTATE_REFRESH_TOKEN=true
TOKEN_ROTATION_GRACE_PERIOD=300      # 5 minutes

# ============================================================================
# AUDIT TRAIL
# ============================================================================

# Audit log retention (in days, default: 365 = 1 year)
AUDIT_LOG_RETENTION_DAYS=365

# ============================================================================
# OTP AND NEW DEVICE VERIFICATION
# ============================================================================

# OTP settings
OTP_LENGTH=6
OTP_EXPIRY_SECONDS=300               # 5 minutes
OTP_MAX_ATTEMPTS=3

# New device verification
NEW_DEVICE_VERIFICATION_REQUIRED=true
DEVICE_FINGERPRINT_ALGORITHM=sha256

# ============================================================================
# SECURITY MONITORING
# ============================================================================

# Health score thresholds (percentages)
HEALTH_SCORE_EXCELLENT=90
HEALTH_SCORE_GOOD=75
HEALTH_SCORE_FAIR=60
HEALTH_SCORE_POOR=0

# Token cleanup settings
EXPIRED_TOKEN_CLEANUP_THRESHOLD=20   # Cleanup when >20% tokens are expired
TOKEN_CLEANUP_BATCH_SIZE=1000
TOKEN_CLEANUP_INTERVAL_HOURS=24

# ============================================================================
# DEVELOPMENT VS PRODUCTION OVERRIDES
# ============================================================================

# For development, you might want to use shorter timeouts for testing:
# DEVICE_FLOW_TIMEOUT=300              # 5 minutes instead of 1 hour
# ACCESS_TOKEN_LIFETIME_SECONDS=300    # 5 minutes instead of 15 minutes
# ACTIVATION_TOKEN_HOURS=1             # 1 hour instead of 24 hours

# For production, consider stricter settings:
# MAX_FAILED_ATTEMPTS=3                # Stricter lockout
# DEVICE_VALIDATION_MIN_SCORE=70       # Higher security score requirement
# LOGIN_RATE_LIMIT_COUNT=5             # Stricter rate limiting
