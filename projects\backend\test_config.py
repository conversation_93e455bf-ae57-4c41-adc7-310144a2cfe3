#!/usr/bin/env python
"""
Test script to verify centralized OAuth2 security configuration
without running the full Django application.
"""

import os
import sys
import django
from pathlib import Path

# Add the project directory to Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(BASE_DIR))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agritram.settings')

# Configure Django with minimal setup
django.setup()

def test_centralized_config():
    """Test that all services are using centralized configuration"""
    print("🔐 Testing Centralized OAuth2 Security Configuration")
    print("=" * 60)
    
    try:
        # Import the centralized config
        from oauth2_auth.config import oauth2_security_config
        
        print("✅ Successfully imported centralized configuration")
        print(f"   Environment: {'Development' if oauth2_security_config.is_development() else 'Production'}")
        
        # Test Device Validation Service
        print("\n📱 Device Validation Service:")
        from oauth2_auth.device_validation_service import DeviceValidationService
        print(f"   ✅ Flow Timeout: {DeviceValidationService.DEVICE_FLOW_TIMEOUT}s")
        print(f"   ✅ Max Device Changes: {DeviceValidationService.MAX_DEVICE_CHANGES}")
        print(f"   ✅ Min Security Score: {DeviceValidationService.DEVICE_VALIDATION_MIN_SCORE}%")
        
        # Test Secure Token Service
        print("\n🎫 Secure Token Service:")
        from oauth2_auth.secure_token_service import SecureTokenService
        print(f"   ✅ Activation Token: {SecureTokenService.TOKEN_EXPIRATION['activation']}h")
        print(f"   ✅ Password Reset Token: {SecureTokenService.TOKEN_EXPIRATION['password_reset']}h")
        print(f"   ✅ Max Attempts (Activation): {SecureTokenService.MAX_ATTEMPTS['activation']}")
        
        # Test Account Lockout Service
        print("\n🔒 Account Lockout Service:")
        from user.account_lockout_service import AccountLockoutService
        print(f"   ✅ Failed Attempt Window: {AccountLockoutService.FAILED_ATTEMPT_WINDOW_HOURS}h")
        print(f"   ✅ Max Failed Attempts: {AccountLockoutService.INITIAL_FAILED_ATTEMPTS_THRESHOLD}")
        print(f"   ✅ Initial Lockout: {AccountLockoutService.INITIAL_LOCKOUT_HOURS}h")
        
        # Test Rate Limiting Service
        print("\n⚡ Rate Limiting Service:")
        from oauth2_auth.rate_limiting_service import RateLimitingService
        print(f"   ✅ Login Rate Limit: {RateLimitingService.RATE_LIMITS['login']['requests']} per {RateLimitingService.RATE_LIMITS['login']['window']}s")
        print(f"   ✅ Registration Rate Limit: {RateLimitingService.RATE_LIMITS['registration']['requests']} per {RateLimitingService.RATE_LIMITS['registration']['window']}s")
        
        # Test Token Invalidation Service
        print("\n🚫 Token Invalidation Service:")
        from oauth2_auth.token_invalidation_service import TokenInvalidationService
        print(f"   ✅ Blacklist Cache Timeout: {TokenInvalidationService.BLACKLIST_CACHE_TIMEOUT}s")
        print(f"   ✅ Security Breach Reason: {TokenInvalidationService.SECURITY_BREACH}")
        
        # Test JWT Rotation Service
        print("\n🔑 JWT Rotation Service:")
        from oauth2_auth.jwt_rotation_service import JWTRotationService
        print(f"   ✅ Access Token Lifespan: {JWTRotationService.ACCESS_TOKEN_LIFESPAN}s ({JWTRotationService.ACCESS_TOKEN_LIFESPAN // 60}min)")
        print(f"   ✅ Refresh Token Lifespan: {JWTRotationService.REFRESH_TOKEN_LIFESPAN}s ({JWTRotationService.REFRESH_TOKEN_LIFESPAN // 86400}d)")
        
        # Test configuration summary
        print("\n📊 Configuration Summary:")
        config_summary = oauth2_security_config.get_config_summary()
        for key, value in config_summary.items():
            if isinstance(value, dict):
                print(f"   ✅ {key.replace('_', ' ').title()}:")
                for sub_key, sub_value in value.items():
                    print(f"      - {sub_key}: {sub_value}")
            else:
                print(f"   ✅ {key.replace('_', ' ').title()}: {value}")
        
        print("\n🎉 SUCCESS: All services are using centralized configuration!")
        print("\n💡 Benefits achieved:")
        print("   ✅ Single source of truth for all security settings")
        print("   ✅ Environment-based configuration support")
        print("   ✅ Easy maintenance and updates")
        print("   ✅ Consistent security parameters across services")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Configuration Error: {e}")
        return False

def test_environment_variables():
    """Test environment variable configuration"""
    print("\n🌍 Environment Variables Test:")
    
    # Test some key environment variables
    test_vars = [
        ('DEVICE_FLOW_TIMEOUT', '3600'),
        ('DEVICE_VALIDATION_MIN_SCORE', '60'),
        ('ACCESS_TOKEN_LIFETIME_SECONDS', '900'),
        ('REFRESH_TOKEN_LIFETIME_SECONDS', '604800'),
    ]
    
    for var_name, default_value in test_vars:
        actual_value = os.environ.get(var_name, default_value)
        print(f"   ✅ {var_name}: {actual_value}")
    
    print("\n💡 To customize configuration:")
    print("   1. Copy .env.security.example to .env")
    print("   2. Modify values in .env file")
    print("   3. Restart the application")

if __name__ == "__main__":
    success = test_centralized_config()
    test_environment_variables()
    
    if success:
        print("\n🚀 Centralized configuration is working perfectly!")
        sys.exit(0)
    else:
        print("\n💥 Configuration test failed!")
        sys.exit(1)
