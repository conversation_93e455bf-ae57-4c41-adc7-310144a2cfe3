#!/usr/bin/env python
"""
Simple test to verify centralized configuration imports work
"""

import sys
from pathlib import Path

# Add the project directory to Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(BASE_DIR))

def test_config_import():
    """Test that we can import the centralized config"""
    try:
        # Test basic import
        from oauth2_auth.config import oauth2_security_config
        print("✅ Successfully imported oauth2_security_config")
        
        # Test configuration values
        print(f"✅ Device Flow Timeout: {oauth2_security_config.DEVICE_FLOW_TIMEOUT}s")
        print(f"✅ Device Validation Min Score: {oauth2_security_config.DEVICE_VALIDATION_MIN_SCORE}%")
        print(f"✅ Access Token Lifetime: {oauth2_security_config.ACCESS_TOKEN_LIFETIME}s")
        print(f"✅ Refresh Token Lifetime: {oauth2_security_config.REFRESH_TOKEN_LIFETIME}s")
        
        # Test token expiration dict
        print("✅ Token Expiration Settings:")
        for token_type, hours in oauth2_security_config.TOKEN_EXPIRATION.items():
            print(f"   - {token_type}: {hours}h")
        
        # Test rate limit settings
        print("✅ Rate Limit Thresholds:")
        for endpoint, count in oauth2_security_config.RATE_LIMIT_THRESHOLDS.items():
            print(f"   - {endpoint}: {count} requests")
        
        print("\n🎉 SUCCESS: Centralized configuration is working!")
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🔐 Testing Centralized OAuth2 Security Configuration")
    print("=" * 60)
    
    success = test_config_import()
    
    if success:
        print("\n🚀 Configuration test passed!")
        print("\n💡 Next steps:")
        print("   1. All services now use centralized configuration")
        print("   2. Modify .env file to customize settings")
        print("   3. Configuration is environment-aware")
        print("   4. Easy to maintain and update")
    else:
        print("\n💥 Configuration test failed!")
        sys.exit(1)
