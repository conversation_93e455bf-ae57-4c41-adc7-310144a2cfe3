#!/usr/bin/env python
"""
Test script to verify token management is using centralized configuration
"""

import sys
from pathlib import Path

# Add the project directory to Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(BASE_DIR))

def test_token_management_config():
    """Test that TokenManager is using centralized configuration"""
    print("🔐 Testing Token Management Centralized Configuration")
    print("=" * 60)
    
    try:
        # Import the centralized config and token manager
        from oauth2_auth.config import oauth2_security_config
        from oauth2_auth.token_management import token_manager
        
        print("✅ Successfully imported centralized configuration and token manager")
        
        # Test that token manager is using centralized config values
        print("\n📊 Token Manager Configuration:")
        
        # Check access token lifetime
        expected_access_lifetime_seconds = oauth2_security_config.ACCESS_TOKEN_LIFETIME
        actual_access_lifetime_seconds = int(token_manager.access_token_lifetime.total_seconds())
        
        print(f"   ✅ Access Token Lifetime:")
        print(f"      - Config: {expected_access_lifetime_seconds}s ({expected_access_lifetime_seconds // 60}min)")
        print(f"      - Token Manager: {actual_access_lifetime_seconds}s ({actual_access_lifetime_seconds // 60}min)")
        print(f"      - Match: {'✅ YES' if expected_access_lifetime_seconds == actual_access_lifetime_seconds else '❌ NO'}")
        
        # Check refresh token lifetime
        expected_refresh_lifetime_seconds = oauth2_security_config.REFRESH_TOKEN_LIFETIME
        actual_refresh_lifetime_seconds = int(token_manager.refresh_token_lifetime.total_seconds())
        
        print(f"   ✅ Refresh Token Lifetime:")
        print(f"      - Config: {expected_refresh_lifetime_seconds}s ({expected_refresh_lifetime_seconds // 86400}d)")
        print(f"      - Token Manager: {actual_refresh_lifetime_seconds}s ({actual_refresh_lifetime_seconds // 86400}d)")
        print(f"      - Match: {'✅ YES' if expected_refresh_lifetime_seconds == actual_refresh_lifetime_seconds else '❌ NO'}")
        
        # Check cleanup batch size
        expected_batch_size = oauth2_security_config.TOKEN_CLEANUP_THRESHOLDS['cleanup_batch_size']
        actual_batch_size = token_manager.cleanup_batch_size
        
        print(f"   ✅ Cleanup Batch Size:")
        print(f"      - Config: {expected_batch_size}")
        print(f"      - Token Manager: {actual_batch_size}")
        print(f"      - Match: {'✅ YES' if expected_batch_size == actual_batch_size else '❌ NO'}")
        
        # Test configuration summary
        print("\n📋 Configuration Summary:")
        print(f"   ✅ Access Token: {actual_access_lifetime_seconds}s")
        print(f"   ✅ Refresh Token: {actual_refresh_lifetime_seconds}s")
        print(f"   ✅ Cleanup Batch: {actual_batch_size}")
        
        # Verify all values match
        all_match = (
            expected_access_lifetime_seconds == actual_access_lifetime_seconds and
            expected_refresh_lifetime_seconds == actual_refresh_lifetime_seconds and
            expected_batch_size == actual_batch_size
        )
        
        if all_match:
            print("\n🎉 SUCCESS: Token Management is using centralized configuration!")
            print("\n💡 Benefits:")
            print("   ✅ Consistent token lifetimes across all services")
            print("   ✅ Environment-configurable cleanup settings")
            print("   ✅ Single source of truth for token management")
            print("   ✅ Easy to modify via .env file")
        else:
            print("\n❌ MISMATCH: Some values don't match centralized configuration")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Configuration Error: {e}")
        return False

def show_configuration_usage():
    """Show how to use the centralized configuration"""
    print("\n🔧 How to Customize Token Management Configuration:")
    print("=" * 60)
    
    print("1. Edit your .env file:")
    print("   ACCESS_TOKEN_LIFETIME_SECONDS=900    # 15 minutes")
    print("   REFRESH_TOKEN_LIFETIME_SECONDS=604800 # 7 days")
    print("   TOKEN_CLEANUP_BATCH_SIZE=1000")
    
    print("\n2. For development, use shorter lifetimes:")
    print("   ACCESS_TOKEN_LIFETIME_SECONDS=300    # 5 minutes")
    print("   REFRESH_TOKEN_LIFETIME_SECONDS=3600  # 1 hour")
    
    print("\n3. For production, use secure defaults:")
    print("   ACCESS_TOKEN_LIFETIME_SECONDS=900    # 15 minutes")
    print("   REFRESH_TOKEN_LIFETIME_SECONDS=604800 # 7 days")
    
    print("\n4. Restart the application to apply changes")

if __name__ == "__main__":
    success = test_token_management_config()
    show_configuration_usage()
    
    if success:
        print("\n🚀 Token Management centralized configuration is working perfectly!")
        sys.exit(0)
    else:
        print("\n💥 Token Management configuration test failed!")
        sys.exit(1)
