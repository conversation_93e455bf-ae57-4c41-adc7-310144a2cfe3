from django.conf import settings
from django.contrib.auth.password_validation import validate_password
from django.contrib.auth.tokens import default_token_generator
from django.core.exceptions import ValidationError
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.encoding import force_bytes, force_str
from django.utils.http import urlsafe_base64_decode, urlsafe_base64_encode
from rest_framework import status
from rest_framework.authentication import SessionAuthentication, TokenAuthentication
from rest_framework.authtoken.models import Token
from rest_framework.decorators import (
    api_view,
    authentication_classes,
    permission_classes,
)
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
import logging

from oauth2_auth.permissions import (
    ProfilePermission
)
from oauth2_auth.config import oauth2_security_config
from agritram.message_utils import StandardErrorResponse, StandardSuccessResponse, handle_serializer_errors, handle_registration_serializer_errors, handle_exception_with_logging, get_frontend_url_by_role
from agritram.exceptions import (
    ValidationException,
    AuthenticationException,
    DuplicateResourceException,
    DeviceVerificationRequiredException,
    raise_validation_error,
    raise_authentication_error,
)

from .models import User
from .serializers import UserSerializer

logger = logging.getLogger(__name__)


@api_view(["POST"])
@permission_classes([AllowAny])
def register(request):
    """
    Enhanced registration with OAuth2 integration, security logging, and email verification
    """
    try:
        # Import OAuth2 utilities and security services
        from oauth2_auth.utils import (
            log_security_event, get_client_ip, generate_device_fingerprint,
        )
        from oauth2_auth.device_validation_service import device_validation_service
        from oauth2_auth.rate_limiting_service import rate_limiting_service
        from oauth2_provider.models import Application
        import secrets

        # Get client information for security logging
        client_ip = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        fingerprint = generate_device_fingerprint(request)

        # Check rate limiting for registration
        rate_check_id = f"{client_ip}_registration"
        is_allowed, rate_info = rate_limiting_service.check_rate_limit(
            rate_check_id, 'registration', request
        )
        if not is_allowed:
            raise_validation_error(
                message="Too many registration attempts",
                details=rate_info.get('message', 'Please try again later'),
                error_code="RATE_LIMITED"
            )

        # Extract registration data
        email = request.data.get('email', '').lower().strip()
        device_id = request.data.get('device_id')
        device_name = request.data.get('device_name', 'Registration Device')

        # Generate device_id if not provided (for device tracking)
        if not device_id:
            device_id = f"reg_device_{secrets.token_urlsafe(16)}"

        # Check for duplicate registration attempts
        if email:
            try:
                existing_user = User.objects.get(email=email)
                if existing_user:
                    log_security_event(
                        user=existing_user,
                        event_type='duplicate_registration_attempt',
                        description='Registration attempt with existing active email',
                        ip_address=client_ip,
                        user_agent=user_agent,
                        metadata={'email': email}
                    )
                    raise DuplicateResourceException(
                        message="User with this email already exists",
                        details="An active account with this email address is already registered"
                    )
            except User.DoesNotExist:
                pass

        # Validate and create user
        serializer = UserSerializer(data=request.data)
        if serializer.is_valid():
            # Create user (inactive by default)
            user = serializer.save()

            # Create OAuth2 application for the user
            try:
                # Get role-specific frontend URL
                frontend_url = get_frontend_url_by_role(user.role)
                application = Application.objects.create(
                    name=f"Agritram App - {user.name}",
                    user=user,
                    client_type=Application.CLIENT_CONFIDENTIAL,
                    authorization_grant_type=Application.GRANT_AUTHORIZATION_CODE,
                    client_id=f"agritram-{user.id}-{secrets.token_urlsafe(8)}",
                    client_secret=secrets.token_urlsafe(32),
                    redirect_uris=f"{frontend_url}/auth/callback/",
                )

                logger.info(f"Created OAuth2 application for user {user.email}: {application.client_id}")

            except Exception as e:
                logger.error(f"Failed to create OAuth2 application for user {user.email}: {str(e)}")
                # Continue with registration even if OAuth2 app creation fails
                application = None

            # Register the registration device (always register since we always have device_id)
            device_registered = False
            try:
                from oauth2_auth.authentication import DeviceAuthenticationService
                DeviceAuthenticationService.register_device(
                    user, device_id, device_name, 'web', request
                )
                device_registered = True
                logger.info(f"Registered device for user {user.email}: {device_id}")
            except Exception as e:
                logger.warning(f"Failed to register device during registration: {str(e)}")

            # Generate secure activation token using new token service
            from oauth2_auth.secure_token_service import secure_token_service
            activation_token, token_obj = secure_token_service.generate_token(
                user=user,
                token_type='activation',
                request=request,
                metadata={
                    'registration_ip': client_ip,
                    'registration_user_agent': user_agent,
                    'device_registered': device_registered
                }
            )
            uid = urlsafe_base64_encode(force_bytes(user.pk))

            # Construct activation URL using role-specific frontend URL
            frontend_url = get_frontend_url_by_role(user.role)
            activation_url = f"{frontend_url}/activate-account/{uid}/{activation_token}/"

            # Track device for cross-flow validation
            device_validation_service.track_registration_device(
                user=user,
                device_id=device_id,
                request=request
            )

            # Log registration event
            log_security_event(
                user=user,
                event_type='user_registered',
                description='New user registration',
                ip_address=client_ip,
                user_agent=user_agent,
                metadata={
                    'email': user.email,
                    'name': user.name,
                    'role': user.role,
                    'device_name': device_name,
                    'fingerprint': fingerprint,
                    'oauth2_app_created': application is not None,
                    'device_registered': device_registered,
                    'device_id': device_id
                }
            )

            # Send enhanced activation email using email service
            from oauth2_auth.email_service import email_service
            email_service.send_registration_activation(user, activation_url, user_agent)

            # Prepare response data
            user_data = {
                "user": serializer.data,
                "activation_required": True,
                "device_registered": device_registered,
                "device_id": device_id,
                "device_name": device_name,
            }

            # Include OAuth2 client info if application was created
            if application:
                user_data["oauth2_client_id"] = application.client_id
                user_data["oauth2_redirect_uri"] = application.redirect_uris

            return StandardSuccessResponse.registration_success(
                message="Registration successful",
                details="Account created and activation email sent to your email address",
                user_data=user_data
            )

        else:
            # Log failed registration attempt
            log_security_event(
                event_type='failed_registration',
                description='Registration failed due to validation errors',
                ip_address=client_ip,
                user_agent=user_agent,
                metadata={
                    'email': email,
                    'errors': serializer.errors,
                    'device_name': device_name
                }
            )
            return handle_registration_serializer_errors(serializer)

    except DuplicateResourceException:
        # Re-raise our custom exceptions to be handled by the exception handler
        raise
    except Exception as e:
        logger.error(f"Registration error: {str(e)}")
        return handle_exception_with_logging(e, "user registration")


@api_view(["POST"])
@permission_classes([AllowAny])
def activate_account(request):
    """
    Enhanced account activation with security logging and OAuth2 integration
    """
    try:
        # Import OAuth2 utilities and security services
        from oauth2_auth.utils import log_security_event, get_client_ip
        from oauth2_auth.authentication import DeviceAuthenticationService
        from oauth2_auth.device_validation_service import device_validation_service
        import secrets

        # Get client information
        client_ip = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')

        # Extract activation data
        uidb64 = request.data.get("uid")
        token = request.data.get("token")
        device_id = request.data.get("device_id")
        device_name = request.data.get("device_name", "Activation Device")

        if not uidb64 or not token:
            log_security_event(
                event_type='invalid_activation_attempt',
                description='Activation attempt with missing parameters',
                ip_address=client_ip,
                user_agent=user_agent,
                metadata={'missing_params': not uidb64 and 'uid' or 'token'}
            )
            raise_validation_error(
                message="Invalid activation link",
                details="Both UID and token are required for account activation"
            )

        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            user = User.objects.get(pk=uid)
        except (TypeError, ValueError, OverflowError, User.DoesNotExist):
            log_security_event(
                event_type='invalid_activation_attempt',
                description='Activation attempt with invalid UID',
                ip_address=client_ip,
                user_agent=user_agent,
                metadata={'uid': uidb64}
            )
            raise_validation_error(
                message="Invalid activation link",
                details="The activation link is malformed or the user does not exist"
            )

        # Check if user is already active
        if user.is_mail_verified:
            log_security_event(
                user=user,
                event_type='activation_attempt_for_active_user',
                description='Duplicate activation attempt for already active user',
                ip_address=client_ip,
                user_agent=user_agent,
                metadata={'email': user.email}
            )
            return StandardSuccessResponse.create_success_response(
                code=StandardSuccessResponse.ACCOUNT_ACTIVATED,
                message="Account is already activated",
                details="Your account was previously activated and is ready to use",
                actions="You can now log in to your account"
            )

        # Verify activation token using secure token service
        from oauth2_auth.secure_token_service import secure_token_service
        is_valid, token_obj, error_message = secure_token_service.validate_token(
            raw_token=token,
            token_type='activation',
            request=request,
            user=user
        )

        if is_valid and token_obj:
            # Validate device consistency for activation
            is_device_valid, device_message, device_details = device_validation_service.validate_activation_device(
                user=user,
                device_id=device_id,
                request=request
            )

            # Log device validation result (but don't block activation unless security score is very low)
            if not is_device_valid and device_details.get('security_score', 100) < 30:
                log_security_event(
                    user=user,
                    event_type='activation_device_validation_failed',
                    description='Device validation failed during activation - blocking activation',
                    ip_address=client_ip,
                    user_agent=user_agent,
                    device_id=device_id,
                    metadata=device_details
                )
                raise_validation_error(
                    message="Device validation failed",
                    details="The device used for activation doesn't match the registration device",
                    error_code="DEVICE_VALIDATION_FAILED"
                )
            # Mark token as used
            secure_token_service.use_token(token_obj, request)

            # Activate the user
            user.is_mail_verified = True
            user.save()

            # Register the activation device if provided
            if device_id:
                try:
                    DeviceAuthenticationService.register_device(
                        user, device_id, device_name, 'web', request
                    )
                except Exception as e:
                    logger.warning(f"Failed to register activation device: {str(e)}")

            # Log successful activation
            log_security_event(
                user=user,
                event_type='account_activated',
                description='User account successfully activated',
                ip_address=client_ip,
                user_agent=user_agent,
                metadata={
                    'email': user.email,
                    'activation_time': timezone.now().isoformat(),
                    'device_registered': device_id is not None
                }
            )

            # Send welcome email using email service
            from oauth2_auth.email_service import email_service
            email_service.send_welcome_email(user, client_ip, user_agent)

            # Get user's OAuth2 application for frontend
            user_oauth_app = None
            try:
                from oauth2_provider.models import Application
                user_oauth_app = Application.objects.filter(user=user).first()
            except Exception as e:
                logger.warning(f"Failed to retrieve OAuth2 application for user {user.email}: {str(e)}")

            # Prepare response data
            response_data = {
                "user": UserSerializer(user).data,
                "device_id": device_id
            }

            # Add OAuth2 credentials if available
            if user_oauth_app:
                response_data["oauth2_client_id"] = user_oauth_app.client_id
                response_data["oauth2_redirect_uri"] = user_oauth_app.redirect_uris

            return StandardSuccessResponse.create_success_response(
                code=StandardSuccessResponse.ACCOUNT_ACTIVATED,
                message="Account activated successfully",
                details="Your account has been activated and is now ready to use",
                actions="You can now log in to access all features",
                data=response_data
            )

        else:
            log_security_event(
                user=user,
                event_type='invalid_activation_token',
                description='Activation attempt with invalid or expired token',
                ip_address=client_ip,
                user_agent=user_agent,
                metadata={
                    'email': user.email,
                    'error': error_message,
                    'token_status': token_obj.status if token_obj else 'not_found'
                }
            )
            raise_validation_error(
                message="Activation link is invalid or has expired",
                details=error_message
            )

    except (ValidationException, AuthenticationException):
        # Re-raise our custom exceptions to be handled by the exception handler
        raise
    except Exception as e:
        logger.error(f"Account activation error: {str(e)}")
        return handle_exception_with_logging(e, "account activation")

@api_view(["POST"])
@permission_classes([AllowAny])
def login(request):
    """
    Enhanced login with OAuth2 integration, device detection, and email notifications
    """
    try:
        # Import OAuth2 utilities and security services
        from oauth2_auth.utils import (
            log_security_event, get_client_ip, generate_device_fingerprint,
            generate_otp, send_otp_email, detect_suspicious_activity
        )
        from oauth2_auth.authentication import DeviceAuthenticationService
        from oauth2_auth.jwt_rotation_service import jwt_rotation_service
        from oauth2_auth.rate_limiting_service import rate_limiting_service
        from oauth2_provider.models import Application
        from django.core.cache import cache
        import secrets

        # Extract login data
        email = request.data.get("email")
        password = request.data.get("password")
        device_id = request.data.get("device_id")
        device_name = request.data.get("device_name", "Unknown Device")
        otp_code = request.data.get("otp_code")  # For device verification

        # Validate required fields
        if not email or not password:
            raise_validation_error(
                message="Email and password are required",
                details="Both email and password fields must be provided for login"
            )

        # Get client information
        client_ip = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        fingerprint = generate_device_fingerprint(request)

        # Check rate limiting for login attempts
        rate_check_id = f"{client_ip}_login"
        is_allowed, rate_info = rate_limiting_service.check_rate_limit(
            rate_check_id, 'login', request
        )
        if not is_allowed:
            raise_validation_error(
                message="Too many login attempts",
                details=rate_info.get('message', 'Please try again later'),
                error_code="RATE_LIMITED"
            )

        try:
            user = User.objects.get(email=email)

            # Check if user account is deleted
            if user.is_deleted:
                log_security_event(
                    user=user,
                    event_type='deleted_user_login_attempt',
                    description='Login attempt by deleted user',
                    ip_address=client_ip,
                    user_agent=user_agent,
                    metadata={'email': email}
                )
                raise_authentication_error(
                    message="Account not found",
                    details="This account is no longer available",
                    error_code="ACCOUNT_DELETED"
                )

            # Check if user account is unverified
            if not user.is_mail_verified:
                log_security_event(
                    user=user,
                    event_type='unverified_user_login_attempt',
                    description='Login attempt by unverified user',
                    ip_address=client_ip,
                    user_agent=user_agent,
                    metadata={'email': email}
                )
                raise_authentication_error(
                    message="Account has not been verified",
                    details="Please verify your account by clicking the link in your email",
                    error_code="ACCOUNT_UNVERIFIED"
                )

            # Check if user is active
            if not user.is_active:
                log_security_event(
                    user=user,
                    event_type='inactive_user_login_attempt',
                    description='Login attempt by inactive user',
                    ip_address=client_ip,
                    user_agent=user_agent,
                    metadata={'email': email}
                )
                raise_authentication_error(
                    message="User account is inactive",
                    details="Please activate your account by clicking the link in your email",
                    error_code="ACCOUNT_INACTIVE"
                )

            # Check account lockout status
            from .account_lockout_service import AccountLockoutService
            lockout_status = AccountLockoutService.check_account_lockout(user)

            if lockout_status['is_locked']:
                log_security_event(
                    user=user,
                    event_type='locked_account_login_attempt',
                    description='Login attempt on locked account',
                    ip_address=client_ip,
                    user_agent=user_agent,
                    metadata={
                        'email': email,
                        'is_permanent': lockout_status['is_permanent'],
                        'remaining_time': lockout_status['remaining_time']
                    }
                )

                if lockout_status['is_permanent']:
                    raise_authentication_error(
                        message="Account permanently locked",
                        details="Your account has been permanently locked due to repeated failed login attempts. Please contact support to unlock your account.",
                        error_code="ACCOUNT_PERMANENTLY_LOCKED"
                    )
                else:
                    raise_authentication_error(
                        message="Account temporarily locked",
                        details=lockout_status['message'],
                        error_code="ACCOUNT_TEMPORARILY_LOCKED",
                        metadata={
                            'remaining_time': lockout_status['remaining_time']
                        }
                    )

            # Verify password
            if not user.check_password(password):
                # Handle failed login attempt with lockout logic
                lockout_info = AccountLockoutService.handle_failed_login(user, request)

                # Check for suspicious activity
                detect_suspicious_activity(user, request, 'failed_login')

                # Prepare error response based on lockout status
                if lockout_info['is_locked']:
                    if lockout_info['is_permanent']:
                        raise_authentication_error(
                            message="Account permanently locked",
                            details="Your account has been permanently locked due to repeated failed login attempts. Please contact support to unlock your account.",
                            error_code="ACCOUNT_PERMANENTLY_LOCKED"
                        )
                    else:
                        raise_authentication_error(
                            message="Account locked due to failed attempts",
                            details=lockout_info['message'],
                            error_code="ACCOUNT_TEMPORARILY_LOCKED",
                            metadata={
                                'lockout_duration': lockout_info['lockout_duration']
                            }
                        )
                else:
                    raise_authentication_error(
                        message="Invalid password",
                        details=f"The password provided is incorrect. {lockout_info['message']}",
                        metadata={
                            'attempts_remaining': lockout_info['attempts_remaining']
                        }
                    )

        except User.DoesNotExist:
            log_security_event(
                event_type='failed_login',
                description='Login attempt with non-existent email',
                ip_address=client_ip,
                user_agent=user_agent,
                metadata={'email': email}
            )
            raise_authentication_error(
                message="User does not exist",
                details="No account found with this email address"
            )

        # Device detection and verification
        if not device_id:
            device_id = f"device_{secrets.token_urlsafe(16)}"

        # Check if this is a new device
        is_new_device = True
        try:
            device = user.device_tokens.get(device_id=device_id)
            is_new_device = False

            # Check if device fingerprint has changed
            if device.fingerprint != fingerprint:
                log_security_event(
                    user=user,
                    event_type='device_fingerprint_changed',
                    description='Device fingerprint changed - potential security issue',
                    ip_address=client_ip,
                    user_agent=user_agent,
                    device_id=device_id,
                    metadata={
                        'old_fingerprint': device.fingerprint,
                        'new_fingerprint': fingerprint
                    }
                )
                is_new_device = True

        except user.device_tokens.model.DoesNotExist:
            is_new_device = True

        # Handle new device verification
        if is_new_device:
            if not otp_code:
                # Generate and send OTP for new device
                otp = generate_otp()

                # Store OTP in cache for verification
                otp_key = f"device_otp_{user.id}_{device_id}"
                cache.set(otp_key, otp, oauth2_security_config.OTP_EXPIRY_SECONDS)

                # Send OTP email
                send_otp_email(user, otp, device_name, client_ip)

                log_security_event(
                    user=user,
                    event_type='new_device_detected',
                    description=f'New device detected, OTP sent: {device_name}',
                    ip_address=client_ip,
                    user_agent=user_agent,
                    device_id=device_id,
                    metadata={
                        'device_name': device_name,
                        'fingerprint': fingerprint
                    }
                )

                raise DeviceVerificationRequiredException(
                    message="New device detected. Please check your email for verification code.",
                    details="A verification code has been sent to your registered email address",
                    device_id=device_id
                )

            else:
                # Verify OTP for new device
                otp_key = f"device_otp_{user.id}_{device_id}"
                stored_otp = cache.get(otp_key)

                if not stored_otp or stored_otp != otp_code:
                    log_security_event(
                        user=user,
                        event_type='invalid_otp',
                        description='Invalid OTP provided for device verification',
                        ip_address=client_ip,
                        user_agent=user_agent,
                        device_id=device_id
                    )
                    raise_validation_error(
                        message="Invalid or expired verification code",
                        details="The OTP code provided is incorrect or has expired"
                    )

                # OTP verified, register device
                DeviceAuthenticationService.register_device(
                    user, device_id, device_name, 'web', request
                )

                # Clear OTP from cache
                cache.delete(otp_key)

                log_security_event(
                    user=user,
                    event_type='device_verified',
                    description=f'Device verified and registered: {device_name}',
                    ip_address=client_ip,
                    user_agent=user_agent,
                    device_id=device_id
                )

        # Create OAuth2 application and generate JWT token (enhanced security)
        try:
            # Get default OAuth2 application or create one
            application, created = Application.objects.get_or_create(
                name="Agritram Default App",
                defaults={
                    'client_type': Application.CLIENT_CONFIDENTIAL,
                    'authorization_grant_type': Application.GRANT_AUTHORIZATION_CODE,
                    'client_id': 'agritram-default-client',
                    'client_secret': secrets.token_urlsafe(32),
                }
            )

            # Generate JWT token pair using rotation service for enhanced security
            token_pair = jwt_rotation_service.generate_token_pair(
                user=user,
                application=application,
                device_id=device_id,
                request=request
            )

            if 'error' in token_pair:
                raise Exception(f"Failed to generate JWT token pair: {token_pair['error']}")

            jwt_token = token_pair['access_token']
            refresh_token = token_pair['refresh_token']

        except Exception as e:
            logger.error(f"Token creation error: {str(e)}")
            return StandardErrorResponse.server_error(
                message="Failed to create authentication token",
                details="Unable to generate JWT token for authentication"
            )

        # Log successful login
        log_security_event(
            user=user,
            event_type='login',
            description='Successful login',
            ip_address=client_ip,
            user_agent=user_agent,
            device_id=device_id,
            metadata={
                'device_name': device_name,
                'authentication_method': 'password'
            }
        )

        # Send login notification email using email service
        from oauth2_auth.email_service import email_service
        email_service.send_login_notification(user, device_name, client_ip, user_agent)

        # Detect suspicious activity
        detect_suspicious_activity(user, request, 'login')

        # Reset failed login attempts on successful login
        user.reset_failed_attempts()

        # Update user last login
        user.last_login = timezone.now()
        user.save(update_fields=['last_login'])

        # Get user's OAuth2 application for frontend
        user_oauth_app = None
        try:
            from oauth2_provider.models import Application
            user_oauth_app = Application.objects.filter(user=user).first()
        except Exception as e:
            logger.warning(f"Failed to retrieve OAuth2 application for user {user.email}: {str(e)}")

        # Prepare enhanced token data with refresh token
        token_data = {
            "access_token": jwt_token,
            "refresh_token": refresh_token,
            "token_type": "Bearer",
            "expires_in": token_pair.get('expires_in', oauth2_security_config.ACCESS_TOKEN_LIFETIME),
            "refresh_expires_in": token_pair.get('refresh_expires_in', oauth2_security_config.REFRESH_TOKEN_LIFETIME),
            "scope": "read write profile email",
            "device_id": device_id
        }

        # Add OAuth2 credentials if available
        if user_oauth_app:
            token_data["oauth2_client_id"] = user_oauth_app.client_id
            token_data["oauth2_redirect_uri"] = user_oauth_app.redirect_uris

        return StandardSuccessResponse.login_success(
            message="Login successful",
            details="Authentication completed and session established",
            user_data=UserSerializer(user).data,
            token_data=token_data
        )

    except (DuplicateResourceException, ValidationException, AuthenticationException, DeviceVerificationRequiredException):
        # Re-raise our custom exceptions to be handled by the exception handler
        raise
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        return handle_exception_with_logging(e, "user login")


@api_view(["GET", "PUT"])
@permission_classes([ProfilePermission])
def update_user(request):
    """
    Update an existing user.
    """
    user = request.user

    if request.method == "GET":
        serializer = UserSerializer(user)
        return StandardSuccessResponse.data_retrieved(
            message="User profile retrieved successfully",
            details="Current user profile information",
            data=serializer.data
        )

    if request.method == "PUT":
        if not request.data:
            raise_validation_error(
                message="No data provided to update",
                details="Request body must contain data to update the user profile"
            )

        serializer = UserSerializer(user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return StandardSuccessResponse.record_updated(
                message="User profile updated successfully",
                details="Profile changes have been saved",
                record_data=serializer.data
            )
        return handle_serializer_errors(serializer)


@api_view(["PUT"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def reset_password(request):
    """
    Allow authenticated users to change their password.
    """
    user = request.user
    old_password = request.data.get("old_password")
    new_password = request.data.get("new_password")

    if not old_password or not new_password:
        raise_validation_error(
            message="Old and new passwords are required",
            details="Both old_password and new_password fields must be provided"
        )

    if not user.check_password(old_password):
        raise_authentication_error(
            message="Old password is incorrect",
            details="The current password provided does not match"
        )

    try:
        validate_password(new_password, user)
    except ValidationError as e:
        raise_validation_error(
            message="New password validation failed",
            details="; ".join(e.messages)
        )

    user.set_password(new_password)
    user.save()
    return StandardSuccessResponse.create_success_response(
        code=StandardSuccessResponse.PASSWORD_CHANGED,
        message="Password changed successfully",
        details="Your password has been updated and is now active",
        actions="You can now log in with your new password"
    )


@api_view(["POST"])
@permission_classes([AllowAny])
def forgot_password(request):
    """
    Handle forgotten passwords by sending a reset link.
    """
    email = request.data.get("email")
    if not email:
        raise_validation_error(
            message="Email is required",
            details="Email address must be provided to reset password"
        )

    try:
        user = User.objects.get(email=email)
    except User.DoesNotExist:
        raise_validation_error(
            message="User with this email does not exist",
            details="No account found with the provided email address"
        )

    # Check if account is locked and prevent password reset during lockout
    from .account_lockout_service import AccountLockoutService
    lockout_status = AccountLockoutService.check_account_lockout(user)

    if lockout_status['is_locked']:
        from oauth2_auth.utils import log_security_event, get_client_ip

        # Log password reset attempt on locked account
        client_ip = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')

        log_security_event(
            user=user,
            event_type='password_reset_attempt_locked_account',
            description='Password reset attempt on locked account',
            ip_address=client_ip,
            user_agent=user_agent,
            metadata={
                'email': email,
                'is_permanent': lockout_status['is_permanent'],
                'remaining_time': lockout_status['remaining_time']
            }
        )

        if lockout_status['is_permanent']:
            raise_authentication_error(
                message="Password reset not available",
                details="Your account is permanently locked. Please contact support to unlock your account before resetting your password.",
                error_code="ACCOUNT_PERMANENTLY_LOCKED"
            )
        else:
            raise_authentication_error(
                message="Password reset temporarily unavailable",
                details=f"Password reset is not available while your account is locked. {lockout_status['message']}",
                error_code="ACCOUNT_TEMPORARILY_LOCKED",
                metadata={
                    'remaining_time': lockout_status['remaining_time']
                }
            )

    # Generate secure password reset token using new token service
    from oauth2_auth.secure_token_service import secure_token_service
    from oauth2_auth.utils import get_client_ip

    client_ip = get_client_ip(request)
    user_agent = request.META.get('HTTP_USER_AGENT', '')

    reset_token, token_obj = secure_token_service.generate_token(
        user=user,
        token_type='password_reset',
        request=request,
        metadata={
            'reset_request_ip': client_ip,
            'reset_request_user_agent': user_agent
        }
    )
    uid = urlsafe_base64_encode(force_bytes(user.pk))

    # Construct password reset URL using role-specific frontend URL
    frontend_url = get_frontend_url_by_role(user.role)
    reset_url = f"{frontend_url}/reset-password/{uid}/{reset_token}/"

    # Send enhanced password reset email using email service
    from oauth2_auth.email_service import email_service
    try:
        email_service.send_password_reset(user, reset_url, client_ip, user_agent)
    except Exception as e:
        logger.error(f"Failed to send password reset email: {str(e)}")
        return StandardErrorResponse.external_service_error(
            message="Failed to send email",
            details="Unable to send password reset email. Please try again later",
            service_name="email_service"
        )

    return StandardSuccessResponse.create_success_response(
        code=StandardSuccessResponse.EMAIL_SENT,
        message="Password reset link sent successfully",
        details="A password reset link has been sent to your email address",
        actions="Please check your email and follow the instructions to reset your password"
    )


@api_view(["POST"])
@permission_classes([AllowAny])
def reset_password_confirm(request):
    """
    Confirm the password reset by setting the new password.
    """
    uidb64 = request.data.get("uid")
    token = request.data.get("token")
    new_password = request.data.get("password")

    if not uidb64 or not token or not new_password:
        raise_validation_error(
            message="Invalid data",
            details="UID, token, and new password are all required"
        )

    try:
        uid = force_str(urlsafe_base64_decode(uidb64))
        user = User.objects.get(pk=uid)
    except (TypeError, ValueError, OverflowError, User.DoesNotExist):
        user = None

    # Verify password reset token using secure token service
    from oauth2_auth.secure_token_service import secure_token_service
    is_valid, token_obj, error_message = secure_token_service.validate_token(
        raw_token=token,
        token_type='password_reset',
        request=request,
        user=user
    )

    if is_valid and token_obj and user is not None:
        try:
            validate_password(new_password, user)
        except ValidationError as e:
            raise_validation_error(
                message="Password validation failed",
                details="; ".join(e.messages)
            )

        # Mark token as used
        secure_token_service.use_token(token_obj, request)

        user.set_password(new_password)
        user.save()

        # Log security event
        from oauth2_auth.utils import log_security_event, get_client_ip
        log_security_event(
            user=user,
            event_type='password_reset_completed',
            description='Password reset completed successfully',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            metadata={
                'token_id': str(token_obj.id),
                'reset_time': timezone.now().isoformat()
            }
        )

        return StandardSuccessResponse.create_success_response(
            code=StandardSuccessResponse.PASSWORD_RESET_SUCCESS,
            message="Password reset successfully",
            details="Your password has been reset and is now active",
            actions="You can now log in with your new password"
        )
    else:
        # Log failed password reset attempt
        from oauth2_auth.utils import log_security_event, get_client_ip
        log_security_event(
            user=user,
            event_type='invalid_password_reset_token',
            description='Password reset attempt with invalid or expired token',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            metadata={
                'error': error_message,
                'token_status': token_obj.status if token_obj else 'not_found'
            }
        )

        raise_validation_error(
            message="Invalid token or user ID",
            details=error_message if error_message else "The password reset link is invalid or has expired"
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def logout(request):
    """
    Log out a user by deleting their token.
    """
    user = request.user
    Token.objects.filter(user=user).delete()
    return StandardSuccessResponse.create_success_response(
        code=StandardSuccessResponse.LOGOUT_SUCCESS,
        message="Logout successful",
        details="You have been successfully logged out",
        actions="Your session has been terminated securely"
    )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def get_user_name(request):
    """
    Get the authenticated user's name.
    """
    user = request.user
    return StandardSuccessResponse.data_retrieved(
        message="User name retrieved successfully",
        details="Current user's display name",
        data={"name": user.name}
    )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def is_connected_wallet(request):
    """
    Check if the user is connected to a wallet.
    """
    user = request.user
    return StandardSuccessResponse.data_retrieved(
        message="Wallet connection status retrieved successfully",
        details="Current wallet connection information",
        data={
            "is_connected": user.wallet_address is not None,
            "wallet_address": user.wallet_address,
        }
    )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def connect_account(request):
    """
    Connect the user to a wallet.
    """
    user = request.user
    account_address = request.data.get("account_address")
    if not account_address:
        raise_validation_error(
            message="Wallet address is required",
            details="Account address must be provided to connect wallet"
        )
    try:
        user.account_address = account_address
        user.save()
    except Exception as e:
        print(e)
        return handle_exception_with_logging(e, "wallet connection")
    return StandardSuccessResponse.create_success_response(
        code=StandardSuccessResponse.RECORD_UPDATED,
        message="Wallet connected successfully",
        details=f"Account address {account_address} has been linked to your profile",
        actions="You can now perform blockchain transactions"
    )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@authentication_classes([SessionAuthentication, TokenAuthentication])
def opt_in_account(request):
    """
    Opt-in to a smart contract.
    """
    user = request.user
    try:
        user.opt_in = True
        user.save()
    except Exception as e:
        print(e)
        return handle_exception_with_logging(e, "opt-in process")
    return StandardSuccessResponse.create_success_response(
        code=StandardSuccessResponse.RECORD_UPDATED,
        message="Opt-in successful",
        details="You have successfully opted into the smart contract",
        actions="You can now participate in all contract features"
    )


@api_view(["POST"])
@permission_classes([AllowAny])
def refresh_token(request):
    """
    Refresh JWT access token using refresh token
    """
    try:
        from oauth2_auth.jwt_rotation_service import jwt_rotation_service
        from oauth2_auth.rate_limiting_service import rate_limiting_service
        from oauth2_auth.utils import get_client_ip

        # Get refresh token from request
        refresh_token = request.data.get("refresh_token")
        if not refresh_token:
            raise_validation_error(
                message="Refresh token is required",
                details="Please provide a valid refresh token",
                error_code="MISSING_REFRESH_TOKEN"
            )

        # Check rate limiting for token refresh
        client_ip = get_client_ip(request)
        rate_check_id = f"{client_ip}_token_refresh"
        is_allowed, rate_info = rate_limiting_service.check_rate_limit(
            rate_check_id, 'token_refresh', request
        )
        if not is_allowed:
            raise_validation_error(
                message="Too many token refresh attempts",
                details=rate_info.get('message', 'Please try again later'),
                error_code="RATE_LIMITED"
            )

        # Refresh the token
        new_tokens = jwt_rotation_service.refresh_access_token(refresh_token, request)

        if 'error' in new_tokens:
            raise_validation_error(
                message="Token refresh failed",
                details=new_tokens['error'],
                error_code="REFRESH_FAILED"
            )

        return StandardSuccessResponse.create_success_response(
            code=StandardSuccessResponse.TOKEN_REFRESHED,
            message="Token refreshed successfully",
            details="New access token generated",
            actions="Use the new access token for API requests",
            data={
                "access_token": new_tokens['access_token'],
                "refresh_token": new_tokens['refresh_token'],
                "token_type": new_tokens['token_type'],
                "expires_in": new_tokens['expires_in'],
                "refresh_expires_in": new_tokens.get('refresh_expires_in'),
                "scope": new_tokens.get('scope')
            }
        )

    except (ValidationException, AuthenticationException):
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {str(e)}")
        return handle_exception_with_logging(e, "token refresh")

